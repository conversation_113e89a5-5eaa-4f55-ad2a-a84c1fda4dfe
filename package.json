{"name": "courses_and_consultations", "version": "1.0.0", "description": "", "main": "index.ts", "scripts": {"dev": "nodemon --require tsconfig-paths/register --exec ts-node src/index.ts"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"bcryptjs": "^3.0.2", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "express-async-handler": "^1.2.0", "express-rate-limit": "^7.5.0", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.13.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.2", "nodemailer": "^6.10.1", "stripe": "^18.1.0", "zod": "^3.24.3"}, "devDependencies": {"@types/cookie-parser": "^1.4.8", "@types/cors": "^2.8.17", "@types/csurf": "^1.11.5", "@types/express": "^5.0.1", "@types/jsonwebtoken": "^9.0.9", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.12", "@types/node": "^22.14.1", "@types/nodemailer": "^6.4.17", "nodemon": "^3.1.9", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3", "typescript-eslint": "^8.30.1"}}
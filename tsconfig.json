{"compilerOptions": {"target": "ES6", "module": "NodeNext", "moduleResolution": "NodeNext", "sourceMap": true, "outDir": "./dist", "rootDir": ".", "resolveJsonModule": true, "strict": true, "esModuleInterop": true, "skipLibCheck": true, "noEmit": true, "allowImportingTsExtensions": true, "forceConsistentCasingInFileNames": true, "baseUrl": ".", "typeRoots": ["./node_modules/@types", "./src/types"], "paths": {"@/*": ["./src/*"]}}, "include": ["src/**/*.ts"], "exclude": ["node_modules"]}